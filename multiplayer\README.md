# PersonPicker Multiplayer

This directory contains the multiplayer implementation for the PersonPicker website.

## Structure

- `/server` - Server-side code for WebSocket communication
- `/public` - Client-side code for multiplayer games
  - `/js` - JavaScript files for multiplayer functionality
  - `/css` - CSS files for multiplayer games
- `/games` - Multiplayer game implementations
  - `/grid-quest` - Multiplayer version of Grid Quest

## How to Use

1. Start the WebSocket server:
   ```
   cd multiplayer/server
   npm install
   node server.js
   ```

2. Open the multiplayer game in your browser:
   ```
   http://localhost:8080/multiplayer/games/grid-quest/
   ```

3. Share the game link with other players to join.

## Documentation

See the [DOCUMENTATION.md](DOCUMENTATION.md) file for detailed information about the multiplayer system and how to implement multiplayer in other games.
