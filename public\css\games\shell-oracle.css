/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --danger-color: #0D0630;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --input-background: #FFFFFF;
    --input-border: #8BBEB2;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
}

.container {
    background-color: var(--card-background);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 90%;
    text-align: center;
}

h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

#description {
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

#shellContainer {
    width: 300px;
    height: 300px;
    margin: 20px auto;
    position: relative;
    cursor: pointer;
    overflow: visible;
}

.shell {
    width: 100%;
    height: 100%;
    overflow: visible;
}

.shell-top {
    transform-origin: center bottom;
    transition: transform 1.5s ease;
}

.pearl {
    position: absolute;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, white, #f0f0f0);
    box-shadow: 0 0 10px rgba(255,255,255,0.8);
    top: 60%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.5s ease;
}

#message {
    font-size: 1.2em;
    margin-top: 20px;
    min-height: 60px;
    opacity: 0;
    transition: opacity 1s ease;
}

@keyframes glowing {
    0% { box-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 20px #00ffff, 0 0 35px #00ffff, 0 0 40px #00ffff, 0 0 50px #00ffff, 0 0 75px #00ffff; }
    100% { box-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px #fff, 0 0 40px #00ffff, 0 0 70px #00ffff, 0 0 80px #00ffff, 0 0 100px #00ffff, 0 0 150px #00ffff; }
}

@media (max-width: 600px) {
    .container {
        padding: 1rem;
    }
    #shellContainer {
        width: 80%;
        max-width: 250px;
    }
} 