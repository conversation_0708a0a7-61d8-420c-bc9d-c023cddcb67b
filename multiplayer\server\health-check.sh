#!/bin/bash
# Health check script for PersonPicker multiplayer server
# This script checks if the server is running and restarts it if needed

# Log file
LOG_FILE="/tmp/personpicker-health-check.log"

# Function to log messages
log_message() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
  echo "$1"
}

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
  log_message "PM2 is not installed. Cannot perform health check."
  exit 1
fi

# Check if the personpicker process is running in PM2
if ! pm2 list | grep -q "personpicker"; then
  log_message "PersonPicker server is not running. Attempting to start..."

  # Get the directory of this script
  SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

  # Start the server with PM2
  cd "$SCRIPT_DIR" || exit 1
  NODE_ENV=production PORT=4000 pm2 start server.js --name personpicker

  # Check if the server started successfully
  if pm2 list | grep -q "personpicker"; then
    log_message "PersonPicker server started successfully."
    pm2 save
  else
    log_message "Failed to start PersonPicker server."
    exit 1
  fi
else
  # Check if the process is online
  if pm2 list | grep "personpicker" | grep -q "online"; then
    log_message "PersonPicker server is running normally."
  else
    log_message "PersonPicker server is in an error state. Restarting..."
    pm2 restart personpicker

    # Check if restart was successful
    if pm2 list | grep "personpicker" | grep -q "online"; then
      log_message "PersonPicker server restarted successfully."
    else
      log_message "Failed to restart PersonPicker server."
      exit 1
    fi
  fi
fi

# Check if the server is listening on the expected port
if ! netstat -tuln | grep -q ":4000"; then
  log_message "Server is not listening on port 4000. Restarting..."
  pm2 restart personpicker

  # Check if restart fixed the issue
  sleep 5
  if netstat -tuln | grep -q ":4000"; then
    log_message "Server is now listening on port 4000 after restart."
  else
    log_message "Server is still not listening on port 4000 after restart."
    exit 1
  fi
fi

log_message "Health check completed successfully."
exit 0
