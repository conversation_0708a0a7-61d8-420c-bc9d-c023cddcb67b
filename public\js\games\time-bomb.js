/**
 * Person Picker - A random name picker with fun games
 * Copyright (C) 2024 PersonPicker.com
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 * 
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

const startBtn = document.getElementById('start-btn');
const resetBtn = document.getElementById('reset-btn');
const currentHolderDiv = document.getElementById('current-holder');
const statusDiv = document.getElementById('status');
const countdownDiv = document.getElementById('countdown');
const bombAnimEl = document.getElementById('bomb-animation');

let participantNames = [];
let bombTimeout;
let passingTimeout;
let countdownInterval;
let currentIndex = 0;
let startTimeGlobal;

function startGame() {
    // Load names from localStorage using the correct key and JSON parsing
    let storedNames = localStorage.getItem('names');
    if (!storedNames) {
        alert('No participant names found. Please use the main page to enter names.');
        return;
    }
    participantNames = JSON.parse(storedNames);
    if (participantNames.length === 0) {
        alert('No participant names found.');
        return;
    }
    
    startBtn.disabled = true;
    resetBtn.style.display = 'inline';
    currentHolderDiv.style.display = 'block';
    
    const totalDuration = 10000; // 10 seconds fixed countdown
    startTimeGlobal = Date.now();
    
    // Initialize live countdown display with enhanced warning states
    countdownInterval = setInterval(() => {
        let elapsed = Date.now() - startTimeGlobal;
        let remaining = Math.max(0, (totalDuration - elapsed)) / 1000;
        countdownDiv.textContent = 'Time left: ' + remaining.toFixed(1) + ' seconds';
        
        // Add warning states
        if (remaining <= 3) {
            document.getElementById('game-container').classList.add('warning');
            countdownDiv.style.color = '#ff0000';
            countdownDiv.style.fontSize = '42px';
        } else if (remaining <= 5) {
            countdownDiv.style.color = '#ff6666';
            countdownDiv.style.fontSize = '38px';
        }
        
        if (remaining <= 0) {
            clearInterval(countdownInterval);
            document.getElementById('game-container').classList.remove('warning');
        }
    }, 100);
    
    // Dramatic passing of the bomb
    function updateHolder() {
        let elapsed = Date.now() - startTimeGlobal;
        if (elapsed >= totalDuration) return;
        
        // Show current holder with a bomb icon that animates each pass
        currentHolderDiv.innerHTML = `<span class="holder-name">${participantNames[currentIndex]}</span> <span class="bomb-icon">💣</span>`;
        let bombIcon = currentHolderDiv.querySelector('.bomb-icon');
        bombIcon.classList.remove('passed');
        void bombIcon.offsetWidth; // Trigger reflow to restart animation
        bombIcon.classList.add('passed');
        
        currentIndex = (currentIndex + 1) % participantNames.length;
        // Gradually slow down the passing frequency (from 100ms to ~300ms)
        let delay = 100 + (elapsed / totalDuration) * 200;
        passingTimeout = setTimeout(updateHolder, delay);
    }
    
    updateHolder();
    
    // Enhanced bomb explosion
    bombTimeout = setTimeout(() => {
        clearTimeout(passingTimeout);
        const selectedElem = currentHolderDiv.querySelector('.holder-name');
        const selected = selectedElem ? selectedElem.textContent : '';
        
        // Enhanced explosion effects
        bombAnimEl.style.display = 'block';
        bombAnimEl.classList.add('explode');
        
        // Add screen shake effect
        document.body.style.animation = 'shake 0.5s cubic-bezier(.36,.07,.19,.97) both';
        
        setTimeout(() => {
            bombAnimEl.style.display = 'none';
            bombAnimEl.classList.remove('explode');
            document.body.style.animation = '';
            statusDiv.innerHTML = '💥 BOOM! The bomb exploded on <span style="color: #ff4444; font-size: 1.2em; font-weight: bold;">' + selected + '</span>! 💥';
            
            // Enhanced confetti effect
            if (typeof confetti === 'function') {
                confetti({
                    particleCount: 150,
                    spread: 180,
                    origin: { y: 0.6 },
                    colors: ['#ff4444', '#ff8888', '#ffcccc', '#ff0000'],
                    startVelocity: 45,
                    gravity: 1.5
                });
                
                // Secondary confetti burst
                setTimeout(() => {
                    confetti({
                        particleCount: 50,
                        spread: 90,
                        origin: { y: 0.7 },
                        colors: ['#ff4444', '#ff8888', '#ffcccc', '#ff0000']
                    });
                }, 200);
            }
        }, 1500);
    }, totalDuration);
}

function resetGame() {
    clearTimeout(passingTimeout);
    clearTimeout(bombTimeout);
    clearInterval(countdownInterval);
    participantNames = [];
    currentIndex = 0;
    startBtn.disabled = false;
    resetBtn.style.display = 'none';
    currentHolderDiv.style.display = 'none';
    statusDiv.textContent = '';
    countdownDiv.textContent = 'Time left: 10.0 seconds';
    currentHolderDiv.innerHTML = '';
}

startBtn.addEventListener('click', startGame);
resetBtn.addEventListener('click', resetGame); 