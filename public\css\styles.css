/* Font optimization */
@link rel="preconnect" href="https://fonts.googleapis.com";
@link rel="preconnect" href="https://fonts.gstatic.com" crossorigin;
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap&font-display=swap');

/* Define color variables for easy theming */
:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --highlight-color: #E6F9AF;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --input-background: #FFFFFF;
    --input-border: var(--accent-color);
    --shadow-sm: 0 1px 2px 0 rgb(13 6 48 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(13 6 48 / 0.1), 0 2px 4px -2px rgb(13 6 48 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(13 6 48 / 0.1), 0 4px 6px -4px rgb(13 6 48 / 0.1);
}

/* Global styles */
body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
    padding: 20px;
    box-sizing: border-box;
}

/* Typography styles */
h1, h2, h3 {
    color: var(--primary-color);
}

h1 {
    text-align: center;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
    font-weight: 600;
}

.subheading {
    text-align: center;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

/* Main container styles */
.container {
    background-color: var(--card-background);
    border-radius: 24px;
    padding: 2.5rem;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    transition: all 0.3s ease-in-out;
    border: 1px solid var(--neutral-color);
    backdrop-filter: blur(8px);
}

/* Name input section styles */
#nameInputs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 1rem;
    padding-right: 0.5rem;
}

.input-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: slideIn 0.3s ease-out;
}

/* Input field styles */
input[type="text"] {
    flex: 1;
    padding: 0.5rem 1rem;
    border: 1px solid var(--input-border);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background-color: var(--input-background);
    color: var(--text-color);
    min-width: 0;
}

input[type="text"]:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(139, 190, 178, 0.2);
    outline: none;
}

/* Button styles */
button, .gameIcon, .removeBtn {
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    border: none;
    border-radius: 5px;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

button {
    font-size: 16px;
    padding: 12px 24px;
    margin: 10px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 12px;
    transition: all 0.2s ease;
    border: none;
    font-weight: 600;
}

button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    background-color: var(--secondary-color);
}

button:active {
    transform: translateY(0);
}

/* Specific button styles */
button.help-button,
button#addNameBtn,
button#launchButton,
button#backButton {
    background-color: var(--accent-color);
    color: white;
    padding: 10px 20px;
    font-size: 1rem;
    margin: 5px;
}

button.help-button:hover,
button#addNameBtn:hover,
button#launchButton:hover,
button#backButton:hover {
    background-color: var(--primary-color);
}

button:disabled,
button#launchButton:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;
}

.removeBtn {
    background-color: #dc3545;
    color: white;
    padding: 0.5rem;
    font-size: 0.9rem;
    border-radius: 8px;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.removeBtn:hover {
    background-color: #c82333;
    transform: scale(1.05);
}

/* Game icon styles */
.gameIcon {
    background-color: var(--background-color);
    padding: 15px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    border: 1px solid var(--neutral-color);
}

.gameIcon:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    border-color: var(--accent-color);
    color: var(--primary-color);
}

.gameIcon:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Specific button styles */
#addNameBtn, #launchButton {
    width: 100%;
    margin: 0.5rem 0;
    padding: 0.75rem;
    border-radius: 8px;
}

#backButton {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background-color: var(--primary-color);
    color: white;
    display: none;
    transition: opacity 0.3s ease-in-out, display 0.3s ease-in-out;
}

/* Game frame styles */
#gameFrame {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    z-index: 1000;
    display: none;
    transition: opacity 0.3s ease-in-out, display 0.3s ease-in-out;
}

#gameIcons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Use cases section styles */
#use-cases {
    margin-top: 2rem;
}

.perfect-for {
    text-align: center;
    color: var(--accent-color);
    margin-bottom: 1.5rem;
}

.use-cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.use-case {
    background-color: var(--card-background);
    padding: 1.5rem;
    border-radius: 16px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--neutral-color);
}

.use-case:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-color);
}

.use-case i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.use-case h3 {
    color: var(--primary-color);
    margin: 0.5rem 0;
}

.use-case p {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* Footer styles */
footer {
    text-align: center;
    margin-top: 2rem;
    font-size: 0.9rem;
    color: #555555;
}

/* Custom scrollbar styles */
#nameInputs::-webkit-scrollbar {
    width: 6px;
}

#nameInputs::-webkit-scrollbar-track {
    background: var(--accent-color);
    border-radius: 10px;
}

#nameInputs::-webkit-scrollbar-thumb {
    background: var(--interactive-color);
    border-radius: 10px;
}

#nameInputs::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* Accessibility class for screen readers */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Animation keyframes */
@keyframes fadeIn {
    from { opacity: 0; transform: translateZ(0); }
    to { opacity: 1; transform: translateZ(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateZ(0); }
    to { opacity: 0; transform: translateZ(0); }
}

@keyframes slideIn {
    from { transform: translate3d(0, 20px, 0); opacity: 0; }
    to { transform: translate3d(0, 0, 0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translate3d(0, 0, 0); opacity: 1; }
    to { transform: translate3d(0, 20px, 0); opacity: 0; }
}

@keyframes confettiFall {
    0% { transform: translate3d(0, 0, 0) rotate(0deg); opacity: 1; }
    100% { transform: translate3d(0, 400px, 0) rotate(720deg); opacity: 0; }
}

@keyframes celebrate {
    0%, 100% { transform: scale3d(1, 1, 1); }
    50% { transform: scale3d(1.1, 1.1, 1); }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
    will-change: opacity, transform;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
    will-change: opacity, transform;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
    will-change: transform, opacity;
}

.slide-out {
    animation: slideOut 0.3s ease-out;
    will-change: transform, opacity;
}

/* Confetti styles */
.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    pointer-events: none;
    will-change: transform, opacity;
}

@keyframes confetti {
    0% { transform: translateY(0) rotate(0deg); opacity: 1; }
    100% { transform: translateY(1000px) rotate(720deg); opacity: 0; }
}

.celebrate {
    animation: celebrate 0.5s ease-in-out 3;
}

/* Wheel of names styles */
.slice path {
    stroke: #fff;
    stroke-width: 2px;
    transition: all 0.3s ease;
}

.slice:hover path {
    filter: brightness(1.1);
}

.slice text {
    font-size: 16px;
    font-weight: bold;
    fill: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Start button styles */
#startButton {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    margin-top: 20px;
}

#startButton:hover {
    background-color: var(--primary-color);
}

#startButton:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Message styles */
.message {
    text-align: center;
    margin-top: 20px;
    font-weight: bold;
}

/* Media queries for responsiveness */
@media (max-width: 600px) {
    body {
        padding: 10px;
    }

    .container {
        padding: 1.5rem;
    }

    #gameIcons {
        gap: 0.5rem;
    }

    .gameIcon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .use-cases-grid {
        grid-template-columns: 1fr;
    }

    h1 {
        font-size: 2rem;
    }

    .subheading {
        font-size: 1rem;
    }

    #backButton {
        top: 10px;
        left: 10px;
        font-size: 0.9rem;
        padding: 8px 16px;
    }
}

@media (max-width: 400px) {
    .container {
        padding: 1rem;
    }

    h1 {
        font-size: 1.75rem;
    }

    .subheading {
        font-size: 0.9rem;
    }

    #gameIcons {
        gap: 0.25rem;
    }

    .gameIcon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    button, input[type="text"] {
        font-size: 0.9rem;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

.focus-visible:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #2E282A;
        --card-background: #2E282A;
        --text-color: #FFFFFF;
        --input-background: #17BEBB;
        --input-border: #FFC914;
    }

    body {
        background: linear-gradient(135deg, var(--text-color) 0%, var(--background-color) 100%);
    }

    .container {
        backdrop-filter: blur(12px);
        border-color: var(--secondary-color);
    }

    .use-case {
        background-color: var(--card-background);
        border-color: var(--secondary-color);
    }

    .use-case:hover {
        border-color: var(--accent-color);
    }

    input[type="text"] {
        background-color: var(--background-color);
        border-color: var(--secondary-color);
    }

    h1, h2, h3 {
        color: var(--accent-color);
    }

    .use-case p {
        color: var(--secondary-color);
    }

    .subheading {
        color: var(--secondary-color);
    }

    button:hover {
        background-color: var(--accent-color);
        color: var(--text-color);
    }
}

/* Print styles */
@media print {
    body {
        background: none;
        color: #000;
    }

    .container {
        box-shadow: none;
    }

    #gameIcons, #backButton {
        display: none;
    }

    h1, h2, h3 {
        color: #000;
    }

    .use-case {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

/* Add spacing for the name entry section */
.enter-names-section {
    margin: 2rem 0;
    width: 100%;
}