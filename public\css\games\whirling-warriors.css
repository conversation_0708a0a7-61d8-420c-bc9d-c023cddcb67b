/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --danger-color: #0D0630;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --input-background: #FFFFFF;
    --input-border: #8BBEB2;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
}

.container {
    text-align: center;
    max-width: 800px;
    width: 100%;
    padding: 1rem;
    box-sizing: border-box;
    margin: 0 auto;
    background-color: var(--card-background);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

#arena-container {
    width: 100%;
    padding-bottom: 100%;
    position: relative;
    margin: 20px auto;
}

#arena {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 10px solid var(--primary-color);
    border-radius: 50%;
    overflow: hidden;
    background: radial-gradient(circle, var(--background-color) 0%, var(--card-background) 100%);
}

.beyblade {
    width: 8.8%;
    height: 0;
    padding-bottom: 8.8%;
    border-radius: 50%;
    position: absolute;
    font-weight: bold;
    font-size: 1vw;
    color: white;
    transition: all 0.1s linear;
    overflow: visible;
    box-sizing: border-box;
}

.beyblade-body {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
}

.spin-effect {
    position: absolute;
    width: 200%;
    height: 200%;
    top: -50%;
    left: -50%;
    background: conic-gradient(
        from 0deg,
        transparent 0deg 30deg,
        rgba(255, 255, 255, 0.8) 40deg 50deg,
        transparent 60deg 120deg,
        rgba(255, 255, 255, 0.8) 130deg 140deg,
        transparent 150deg 210deg,
        rgba(255, 255, 255, 0.8) 220deg 230deg,
        transparent 240deg 300deg,
        rgba(255, 255, 255, 0.8) 310deg 320deg,
        transparent 330deg 360deg
    );
}

.beyblade span {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 2px;
    text-shadow: 
        -1px -1px 0 #000,
        1px -1px 0 #000,
        -1px 1px 0 #000,
        1px 1px 0 #000;
}

#message {
    font-size: 18px;
    margin: 20px 0;
    min-height: 50px;
}

#startButton {
    font-size: 18px;
    padding: 10px 20px;
    cursor: pointer;
    margin: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

#startButton:hover {
    background-color: var(--secondary-color);
}

#startButton:disabled {
    background-color: var(--input-border);
    cursor: not-allowed;
}

@media (max-width: 600px) {
    .beyblade {
        font-size: 2vw;
    }
}

#winnerMessage {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-top: 20px;
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.5s ease-in-out;
}

#winnerMessage.show {
    opacity: 1;
    transform: scale(1);
} 