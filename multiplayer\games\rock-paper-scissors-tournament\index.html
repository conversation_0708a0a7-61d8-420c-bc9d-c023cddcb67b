<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiplayer Rock Paper Scissors Tournament</title>
    <link rel="stylesheet" href="/public/css/games/rock-paper-scissors-tournament.css">
    <link rel="stylesheet" href="/multiplayer/public/css/multiplayer.css">
    <script src="/public/js/confetti.js"></script>
    <style>
        /* Additional styles for multiplayer version */
        .player-choice-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .choice-btn {
            font-size: 24px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--accent-color);
            color: white;
            border: none;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .choice-btn:hover {
            transform: scale(1.1);
        }

        .choice-btn:active {
            transform: scale(0.95);
        }

        .choice-btn.selected {
            background-color: var(--primary-color);
            box-shadow: 0 0 10px var(--accent-color);
        }

        .match-container {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            background-color: var(--background-color);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .match-title {
            text-align: center;
            margin-bottom: 15px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .waiting-message {
            text-align: center;
            margin: 20px 0;
            font-style: italic;
            color: var(--secondary-color);
        }

        /* Improved Lobby UI Styles */
        .lobby-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            padding: 1rem;
        }

        .player-name-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4f8 100%);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(24, 49, 79, 0.1);
            border: 1px solid rgba(139, 190, 178, 0.2);
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .input-group label {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .name-input, .room-input {
            padding: 0.75rem 1rem;
            border: 2px solid var(--input-border);
            border-radius: 12px;
            font-size: 1rem;
            font-family: 'Poppins', sans-serif;
            transition: all 0.3s ease;
            background: white;
        }

        .name-input:focus, .room-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(139, 190, 178, 0.1);
            transform: translateY(-1px);
        }

        .divider {
            text-align: center;
            margin: 2rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--accent-color), transparent);
        }

        .divider-text {
            background: var(--background-color);
            color: var(--secondary-color);
            padding: 0 1rem;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .action-cards {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
            margin-top: 1.5rem;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        @media (min-width: 768px) {
            .action-cards {
                grid-template-columns: 1fr 1fr;
                max-width: 800px;
                gap: 2rem;
            }
        }

        .action-card {
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 8px 24px rgba(24, 49, 79, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
        }

        .action-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(24, 49, 79, 0.12);
            border-color: var(--accent-color);
        }

        .create-card:hover::before {
            background: linear-gradient(90deg, var(--success-color), var(--accent-color));
        }

        .join-card:hover::before {
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
        }

        .card-icon {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 1rem;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .card-content h3 {
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0 0 0.5rem 0;
            text-align: center;
        }

        .card-content p {
            color: var(--secondary-color);
            font-size: 0.85rem;
            margin: 0 0 1.5rem 0;
            text-align: center;
            line-height: 1.4;
        }

        .action-button {
            width: 100%;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 12px;
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-create {
            background: linear-gradient(135deg, var(--success-color), #45a049);
            color: white;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
        }

        .btn-join {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            color: white;
            box-shadow: 0 4px 12px rgba(56, 78, 119, 0.3);
        }

        .btn-join:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(56, 78, 119, 0.4);
        }

        .join-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .join-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .clear-button {
            padding: 0.5rem;
            border: 2px solid var(--accent-color);
            background: transparent;
            color: var(--accent-color);
            border-radius: 8px;
            font-family: 'Poppins', sans-serif;
            font-weight: 500;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.3rem;
        }

        .clear-button:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-1px);
        }

        .button-icon {
            font-size: 0.9rem;
        }

        /* Join Room Form Styles */
        .join-room-form {
            margin-top: 2rem;
            animation: slideDown 0.3s ease-out;
        }

        .form-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 24px rgba(24, 49, 79, 0.12);
            border: 2px solid var(--accent-color);
            max-width: 500px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
        }

        .form-card h3 {
            color: var(--primary-color);
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        /* Room Name Entry Styles */
        .room-name-entry {
            margin-bottom: 2rem;
            animation: slideDown 0.3s ease-out;
        }

        .name-entry-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 24px rgba(24, 49, 79, 0.12);
            border: 2px solid var(--accent-color);
            max-width: 500px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .name-entry-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--success-color), var(--accent-color));
        }

        .name-entry-card h3 {
            color: var(--primary-color);
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        .name-entry-card .direct-join-message {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 2px solid #4caf50;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
            color: #2e7d32;
            font-weight: 500;
        }

        .name-entry-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .name-entry-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .action-cards {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .lobby-container {
                padding: 0.5rem;
            }

            .player-name-section {
                padding: 1rem;
                margin-bottom: 1.5rem;
            }

            .action-card {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .card-icon {
                font-size: 1.5rem;
            }

            .card-content h3 {
                font-size: 1rem;
            }

            .card-content p {
                font-size: 0.8rem;
            }

            .action-button {
                padding: 0.6rem 0.8rem;
                font-size: 0.85rem;
            }
        }

        /* Tournament Animation Styles (from single-player version) */
        .round {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 40px;
            width: 100%;
        }

        .match {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 10px 50px 10px;
            padding: 15px;
            background-color: var(--background-color);
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            position: relative;
            min-width: 200px;
            border: 2px solid transparent;
        }

        .match:hover {
            transform: translateY(-5px);
            border-color: var(--accent-color);
        }

        .player {
            display: flex;
            align-items: center;
            margin: 8px 0;
            justify-content: space-between;
            width: 100%;
            padding: 5px 10px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
        }

        .player-name {
            font-weight: 500;
            color: var(--primary-color);
        }

        .choice {
            font-size: 28px;
            width: 45px;
            height: 45px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            background-color: var(--accent-color);
            color: white;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .choice.winner {
            transform: scale(1.3);
            box-shadow: 0 0 20px var(--accent-color);
            background: linear-gradient(135deg, var(--success-color), var(--accent-color));
            animation: winnerPulse 1s ease-in-out;
        }

        @keyframes winnerPulse {
            0%, 100% { transform: scale(1.3); }
            50% { transform: scale(1.4); }
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            25% { transform: translateY(-8px); }
            50% { transform: translateY(0); }
            75% { transform: translateY(-4px); }
        }

        .bouncing {
            animation: bounce 0.8s ease-in-out infinite;
        }

        .match-result {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            opacity: 0;
            transition: opacity 0.4s ease, transform 0.4s ease;
            white-space: nowrap;
            z-index: 10;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .match-result.show {
            opacity: 1;
            transform: translateX(-50%) translateY(-8px);
        }

        .final-match {
            border: 3px solid var(--accent-color);
            padding: 25px;
            animation: finalMatchPulse 2s infinite;
            background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
        }

        @keyframes finalMatchPulse {
            0% {
                box-shadow: 0 0 0 0 rgba(139, 190, 178, 0.7);
                border-color: var(--accent-color);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(139, 190, 178, 0);
                border-color: var(--secondary-color);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(139, 190, 178, 0);
                border-color: var(--accent-color);
            }
        }

        .countdown {
            font-size: 56px;
            font-weight: bold;
            color: var(--accent-color);
            text-align: center;
            margin: 20px 0;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            animation: countdownPulse 1s ease-in-out;
        }

        .countdown.show {
            opacity: 1;
        }

        @keyframes countdownPulse {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
        }

        .final-result {
            font-size: 28px;
            font-weight: bold;
            color: var(--primary-color);
            text-align: center;
            margin: 20px 0;
            opacity: 0;
            transition: opacity 1s ease-in-out;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .final-result.show {
            opacity: 1;
            animation: finalResultAppear 1s ease-out;
        }

        @keyframes finalResultAppear {
            0% { transform: translateY(20px) scale(0.9); opacity: 0; }
            60% { transform: translateY(-5px) scale(1.05); }
            100% { transform: translateY(0) scale(1); opacity: 1; }
        }

        .bye-match {
            opacity: 0.8;
            background: linear-gradient(135deg, rgba(76, 217, 100, 0.1) 0%, rgba(139, 190, 178, 0.1) 100%);
            border-color: rgba(76, 217, 100, 0.3);
        }

        .bye-player {
            font-style: italic;
            color: var(--secondary-color);
            opacity: 0.7;
        }

        /* Choice animation for reveals */
        .choice-reveal {
            animation: choiceReveal 0.6s ease-out;
        }

        @keyframes choiceReveal {
            0% { transform: scale(0.5) rotate(-180deg); opacity: 0; }
            60% { transform: scale(1.2) rotate(0deg); }
            100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }

        /* Tournament bracket styling improvements */
        #tournament {
            margin-top: 2rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4f8 100%);
            border-radius: 20px;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        /* Player highlighting during their turn */
        .player.active-player {
            background-color: rgba(139, 190, 178, 0.1);
            border-left: 4px solid var(--accent-color);
        }

        /* Tie animation */
        .tie-animation {
            animation: tieShake 0.5s ease-in-out;
        }

        @keyframes tieShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Choice made indicator */
        .choice-made {
            animation: choiceMade 0.5s ease-out;
        }

        @keyframes choiceMade {
            0% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(10deg); }
            100% { transform: scale(1) rotate(0deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Multiplayer Rock Paper Scissors Tournament</h1>
        <div id="description">
            Battle it out in a tournament-style Rock Paper Scissors game. Choose your move and see who emerges victorious!
        </div>

        <!-- Connection Status -->
        <div id="connectionStatus" class="connection-status connection-disconnected">Disconnected</div>

        <!-- Lobby Section -->
        <div id="lobbySection" class="multiplayer-section">
            <div class="lobby-container">
                <!-- Player Name Section (shared) -->
                <div class="player-name-section">
                    <div class="input-group">
                        <label for="playerName">👤 Your Name</label>
                        <input type="text" id="playerName" placeholder="Enter your name" class="name-input">
                    </div>
                    <div class="emoji-selector-container">
                        <label class="emoji-selector-label">Choose your emoji:</label>
                        <div id="emojiSelector" class="emoji-selector">
                            <!-- Emojis will be added here via JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="divider">
                    <span class="divider-text">Choose an option</span>
                </div>

                <!-- Action Cards Container -->
                <div class="action-cards">
                    <!-- Create Room Card -->
                    <div class="action-card create-card">
                        <div class="card-icon">🎮</div>
                        <div class="card-content">
                            <h3>Create New Room</h3>
                            <p>Start a new tournament and invite friends to join</p>
                            <button id="createRoomBtn" class="action-button btn-create">
                                <span class="button-icon">➕</span>
                                Create Room
                            </button>
                        </div>
                    </div>

                    <!-- Join Room Card -->
                    <div class="action-card join-card">
                        <div class="card-icon">🚪</div>
                        <div class="card-content">
                            <h3>Join Existing Room</h3>
                            <p>Enter a room ID to join an ongoing tournament</p>
                            <button id="joinRoomBtn" class="action-button btn-join">
                                <span class="button-icon">🔗</span>
                                Join Room
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Join Room Form (Hidden by default) -->
                <div id="joinRoomForm" class="join-room-form hidden">
                    <div class="form-card">
                        <h3>Join Tournament Room</h3>
                        <div class="join-form">
                            <div class="input-group">
                                <label for="roomIdInput">Room ID:</label>
                                <input type="text" id="roomIdInput" placeholder="Enter room ID" class="room-input" style="text-transform: uppercase;">
                            </div>
                            <div class="emoji-selector-container">
                                <label class="emoji-selector-label">Choose your emoji:</label>
                                <div id="joinEmojiSelector" class="emoji-selector">
                                    <!-- Emojis will be added here via JavaScript -->
                                </div>
                            </div>
                            <div class="join-actions">
                                <button id="confirmJoinBtn" class="action-button btn-join">
                                    <span class="button-icon">✅</span>
                                    Join Tournament
                                </button>
                                <button id="cancelJoinBtn" class="clear-button">
                                    <span class="button-icon">❌</span>
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Room Section -->
        <div id="roomSection" class="multiplayer-section hidden">
            <div class="room-info">
                <div>Room: <span id="roomId" class="room-id">ABCDEF</span></div>
                <div id="copyLink" class="copy-link">Copy Link</div>
            </div>

            <!-- Name Entry Interface (shown when user hasn't joined yet) -->
            <div id="roomNameEntry" class="room-name-entry hidden">
                <div class="name-entry-card">
                    <h3>Join this Tournament Room</h3>
                    <div class="direct-join-message">
                        <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">🎯</div>
                        <div>Enter your name and pick an emoji to join the tournament!</div>
                        <div style="font-size: 0.9rem; margin-top: 0.5rem; opacity: 0.8;">
                            You can see other players below who are already in the room.
                        </div>
                    </div>
                    <div class="name-entry-form">
                        <div class="input-group">
                            <label for="roomPlayerName">👤 Your Name</label>
                            <input type="text" id="roomPlayerName" placeholder="Enter your name" class="name-input">
                        </div>
                        <div class="emoji-selector-container">
                            <label class="emoji-selector-label">Choose your emoji:</label>
                            <div id="roomEmojiSelector" class="emoji-selector">
                                <!-- Emojis will be added here via JavaScript -->
                            </div>
                        </div>
                        <div class="name-entry-actions">
                            <button id="joinFromRoomBtn" class="action-button btn-join">
                                <span class="button-icon">✅</span>
                                Join Tournament
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="playerList" class="player-list">
                <!-- Players will be listed here -->
            </div>

            <div id="hostControls" class="multiplayer-controls hidden">
                <button id="startGameBtn" class="btn-success">Start Tournament</button>
            </div>

            <div class="multiplayer-controls">
                <button id="leaveRoomBtn" class="btn-danger">Leave Room</button>
            </div>
        </div>

        <!-- Game Section -->
        <div id="gameSection" class="multiplayer-section hidden">
            <div id="turnIndicator" class="turn-indicator">Waiting for tournament to start...</div>

            <!-- Current Match -->
            <div id="currentMatch" class="match-container hidden">
                <div class="match-title">Your Match</div>
                <div id="matchInfo"></div>

                <!-- Choice Controls -->
                <div id="playerChoiceControls" class="player-choice-controls hidden">
                    <button class="choice-btn" data-choice="✊">✊</button>
                    <button class="choice-btn" data-choice="✋">✋</button>
                    <button class="choice-btn" data-choice="✌️">✌️</button>
                </div>

                <div id="waitingMessage" class="waiting-message hidden">
                    Waiting for your opponent to make a choice...
                </div>
            </div>

            <!-- Tournament Bracket -->
            <div id="tournament"></div>

            <!-- Countdown and Results -->
            <div id="countdown" class="countdown"></div>
            <div id="finalResult" class="final-result"></div>
            <div id="message"></div>

            <div class="multiplayer-controls">
                <button id="newGameBtn" class="hidden">New Tournament</button>
            </div>
        </div>

        <!-- Status Message -->
        <div id="statusMessage" class="status-message hidden"></div>
    </div>

    <script src="/multiplayer/public/js/multiplayer-client.js"></script>
    <script src="/multiplayer/games/rock-paper-scissors-tournament/rock-paper-scissors-tournament-multiplayer.js"></script>
</body>
</html>
