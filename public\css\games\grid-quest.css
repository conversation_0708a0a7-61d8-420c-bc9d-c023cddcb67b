/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --danger-color: #0D0630;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --input-background: #FFFFFF;
    --input-border: #8BBEB2;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
}

.container {
    background-color: var(--card-background);
    border-radius: 20px;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 95%;
    width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

#description {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    width: 100%;
    aspect-ratio: 1 / 1;
}

.cell {
    aspect-ratio: 1 / 1;
    background-color: var(--input-background);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid var(--input-border);
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.cell:hover {
    background-color: var(--accent-color);
    color: white;
}

.cell.hit {
    background-color: var(--primary-color);
    color: white;
}

.cell.miss {
    background-color: var(--danger-color);
    color: white;
}

.cell.reference {
    background-color: var(--secondary-color);
    color: white;
    cursor: default;
}

#message {
    font-size: 16px;
    margin: 1rem 0;
    min-height: 40px;
    text-align: center;
    color: var(--text-color);
}

button {
    font-size: 16px;
    padding: 8px 16px;
    cursor: pointer;
    margin: 10px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    transition: background-color 0.3s ease, transform 0.1s ease;
}

button:hover {
    opacity: 0.9;
}

button:active {
    transform: scale(0.98);
}

#teamList {
    margin: 10px 0;
    text-align: center;
}

.team-member {
    display: inline-block;
    margin: 3px;
    padding: 3px 8px;
    background-color: var(--input-background);
    color: var(--text-color);
    border-radius: 5px;
    font-size: 0.9rem;
}

@keyframes celebrate {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.celebrate {
    animation: celebrate 0.5s ease-in-out 3;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #f00;
    opacity: 0;
}

.winner-highlight {
    background-color: var(--accent-color) !important;
    color: white !important;
    box-shadow: 0 0 10px var(--accent-color);
} 