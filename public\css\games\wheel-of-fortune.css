/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --danger-color: #0D0630;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --input-background: #FFFFFF;
    --input-border: #8BBEB2;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
    padding: 20px;
    box-sizing: border-box;
}

.container {
    background-color: var(--card-background);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 100%;
    text-align: center;
}

h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

#description {
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

#wheel-container {
    position: relative;
    width: 100%;
    padding-bottom: 100%;
    max-width: 500px;
    margin: 20px auto;
}

#chart {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

#question {
    margin-top: 20px;
    font-size: 24px;
    font-weight: bold;
    color: var(--text-color);
    transition: all 0.5s ease;
}

.slice path {
    stroke: #fff;
    stroke-width: 2px;
    transition: all 0.3s ease;
}

.slice:hover path {
    filter: brightness(1.1);
}

.slice text {
    font-size: 16px;
    font-weight: bold;
    fill: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

#spin-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-color);
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
}

#spin-button:hover {
    background: var(--secondary-color);
    box-shadow: 0 6px 8px rgba(0,0,0,0.15);
}

#arrow {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 40px solid var(--text-color);
    z-index: 5;
    filter: drop-shadow(0 3px 3px rgba(0,0,0,0.3));
}

@keyframes celebrate {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.celebrate {
    animation: celebrate 0.5s ease-in-out 3;
}

@media (max-width: 600px) {
    #question {
        font-size: 20px;
    }
    #spin-button {
        width: 60px;
        height: 60px;
        font-size: 14px;
    }
    #arrow {
        border-left-width: 20px;
        border-right-width: 20px;
        border-top-width: 40px;
    }
} 