/*!
 * Person Picker Multiplayer Hub (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

document.addEventListener('DOMContentLoaded', () => {
    // Initialize connection status
    const connectionStatus = document.getElementById('connectionStatus');
    
    // Try to connect to the WebSocket server
    const client = new MultiplayerClient({
        debug: true
    });
    
    // Update connection status
    updateConnectionStatus('connecting');
    
    client.connect()
        .then(() => {
            updateConnectionStatus('connected');
        })
        .catch(error => {
            console.error('Failed to connect:', error);
            updateConnectionStatus('disconnected');
        });
    
    // Event listeners for connection status
    client.on('connected', () => {
        updateConnectionStatus('connected');
    });
    
    client.on('disconnected', () => {
        updateConnectionStatus('disconnected');
    });
    
    client.on('reconnected', () => {
        updateConnectionStatus('connected');
    });
    
    // Update connection status display
    function updateConnectionStatus(status) {
        connectionStatus.className = 'status-indicator';
        
        switch (status) {
            case 'connected':
                connectionStatus.classList.add('connected');
                connectionStatus.textContent = 'Connected to Server';
                break;
            case 'disconnected':
                connectionStatus.classList.add('disconnected');
                connectionStatus.textContent = 'Disconnected';
                break;
            case 'connecting':
                connectionStatus.classList.add('connecting');
                connectionStatus.textContent = 'Connecting...';
                break;
        }
    }
    
    // Add animation to steps
    const steps = document.querySelectorAll('.step');
    steps.forEach((step, index) => {
        step.style.animationDelay = `${index * 0.2}s`;
    });
    
    // Add animation to game cards
    const gameCards = document.querySelectorAll('.game-card');
    gameCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
});
