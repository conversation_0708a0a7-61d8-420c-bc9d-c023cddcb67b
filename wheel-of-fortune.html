<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wheel of Fortune</title>
    <link rel="stylesheet" href="/public/css/games/wheel-of-fortune.css">
    <script src="/public/js/confetti.js"></script>
</head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-CXC0589Q5T"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-CXC0589Q5T');
</script>
<body>
    <div class="container">
        <h1>Wheel of Fortune</h1>
        <div id="description">The Wheel of Fortune is a random name picker that selects a winner by spinning a wheel. Add names, click spin, and watch as the wheel chooses randomly. Great for team selection, prize draws, or any situation needing an impartial choice.</div>
        <div id="wheel-container">
            <div id="chart"></div>
            <div id="arrow"></div>
        </div>
        <div id="question"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/5.16.0/d3.min.js" integrity="sha512-FHsFVKQ/T1KWJDGSbrUhTJyS1ph3eRrxI228ND0EGaEp6v4a/vGwPWd3Dtd/+9cI7ccofZvl/wulICEurHN1pg==" crossorigin="anonymous"></script>
    <script src="/public/js/games/wheel-of-fortune.js"></script>
</body>
</html>
