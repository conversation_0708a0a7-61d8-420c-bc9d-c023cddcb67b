# Person Picker

[![Visit Person Picker](https://img.shields.io/badge/Visit-PersonPicker.com-blue?style=for-the-badge)](https://personpicker.com)

## The Ultimate Random Name Selector for Fair Decisions

Person Picker is an open-source web application designed to make fair selections easy and fun. Whether you're assigning tasks, deciding who runs standup, choosing presenters, or making group decisions, our random selector games ensure everyone gets an equal chance - and has fun doing it!

## 🎯 Key Features

- **Multiple Selection Games**: Choose from a variety of games to pick names.
- **Fair and Unbiased**: Ensure completely random selections for any situation.
- **User-Friendly Interface**: Easy to use, no sign-up required, no ads.
- **Open Source**: Contribute and help improve the website.

## 🚀 Perfect For

- Team Collaboration
- Classroom Activities
- Decision Making
- Winner Selection
- And much more!

## 🔗 Links

- [Person Picker Website](https://personpicker.com)
- [Report a Bug](https://github.com/josh-abram/personpicker.com/issues)
- [Request a Feature](https://github.com/josh-abram/personpicker.com/issues)


## 📜 The Origin Story

Person Picker was born from a pressing issue in modern tech life: who runs the daily standup? This indecision naturally led to outsourcing the task to automation.

Armed with a Claude AI subscription, I cobbled together a basic Wheel of Fortune app. Suddenly, our standups had the dramatic flair of an extremely low-budget game show. It was absurd. It was unnecessary. It was great.

But why stop at spinning wheels? Over time, new games would show up to standup. Need a presenter? Race some pixelated emoji dinosaurs! Important decision? Consult the clamshell...

Before we knew it, it turned into a website of increasingly bizarre selector games to decide the morning standup leader. We have solved office indecision forever.

[Visit Person Picker](https://personpicker.com) | [Contribute on GitHub](https://github.com/josh-abram/personpicker.com)