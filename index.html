<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Person Picker - Random Name Selector</title>
    
    <!-- Redirect to welcome page -->
    <meta http-equiv="refresh" content="0;url=/welcome.html">
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Person Picker is the ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more. Try our fun selection games now!">
    <meta name="keywords" content="random name picker, name selector, random picker, team selection, classroom tool">
    <meta name="author" content="Person Picker">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://personpicker.com/">
    <meta property="og:title" content="Random Person Picker - Fair Selection Made Fun">
    <meta property="og:description" content="The ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more.">
    <meta property="og:image" content="https://personpicker.com/android-chrome-512x512.png">
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://personpicker.com/">
    <meta name="twitter:title" content="Random Person Picker - Fair Selection Made Fun">
    <meta name="twitter:description" content="The ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more.">
    <meta name="twitter:image" content="https://personpicker.com/android-chrome-512x512.png">
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-CXC0589Q5T"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-CXC0589Q5T');
    </script>
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #18314F 0%, #384E77 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            color: white;
            text-align: center;
        }
        
        .redirect-message {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 10px;
            max-width: 500px;
        }
        
        h1 {
            margin-bottom: 1rem;
        }
        
        p {
            margin-bottom: 1.5rem;
        }
        
        a {
            color: #8BBEB2;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="redirect-message">
        <h1>Person Picker</h1>
        <p>Redirecting to the welcome page...</p>
        <p>If you are not redirected automatically, <a href="/welcome.html">click here</a>.</p>
    </div>
</body>
</html>
