/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --danger-color: #0D0630;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
    padding: 20px;
    box-sizing: border-box;
}

.container {
    background-color: var(--card-background);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 100%;
}

h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

#description {
    text-align: center;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

#tournament {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.round {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 40px;
    width: 100%;
}

.match {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 10px 50px 10px;
    padding: 10px;
    background-color: var(--background-color);
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    position: relative;
    min-width: 200px;
}

.match:hover {
    transform: translateY(-5px);
}

.player {
    display: flex;
    align-items: center;
    margin: 5px 0;
}

.player-name {
    margin-right: 10px;
}

.choice {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: var(--accent-color);
    color: white;
    transition: transform 0.3s ease;
}

.choice.winner {
    transform: scale(1.2);
    box-shadow: 0 0 10px var(--accent-color);
}

#message {
    text-align: center;
    margin-top: 20px;
    font-weight: bold;
    color: var(--primary-color);
}

#startButton {
    display: block;
    margin: 20px auto;
    padding: 10px 20px;
    font-size: 18px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#startButton:hover {
    background-color: var(--primary-color);
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.bouncing {
    animation: bounce 0.5s ease infinite;
}

.match-result {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--accent-color);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
    white-space: nowrap;
    z-index: 10;
}

.match-result.show {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

.final-match {
    border: 3px solid var(--accent-color);
    padding: 20px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(139, 190, 178, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(139, 190, 178, 0); }
    100% { box-shadow: 0 0 0 0 rgba(139, 190, 178, 0); }
}

.countdown {
    font-size: 48px;
    font-weight: bold;
    color: var(--accent-color);
    text-align: center;
    margin-top: 20px;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.countdown.show {
    opacity: 1;
}

.final-result {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    text-align: center;
    margin-top: 20px;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.final-result.show {
    opacity: 1;
}

.odd-player-notice {
    background-color: var(--accent-color);
    color: white;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}

.bye-match {
    opacity: 0.7;
    background-color: rgba(76, 217, 100, 0.1);
}

.bye-player {
    font-style: italic;
    color: var(--secondary-color);
} 