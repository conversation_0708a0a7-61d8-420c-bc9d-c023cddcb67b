# PersonPicker Multiplayer Documentation

This document explains how the multiplayer system works and provides instructions for implementing multiplayer functionality in other games.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [WebSocket Server](#websocket-server)
3. [Client Library](#client-library)
4. [Message Protocol](#message-protocol)
5. [Implementing Multiplayer in a Game](#implementing-multiplayer-in-a-game)
6. [Deployment](#deployment)

## Architecture Overview

The PersonPicker multiplayer system uses a client-server architecture with WebSockets for real-time communication:

- **WebSocket Server**: A Node.js server that handles connections, room management, and message routing
- **Client Library**: A JavaScript library that provides an easy-to-use interface for connecting to the server
- **Game Implementation**: Game-specific code that uses the client library to implement multiplayer functionality

The system follows these principles:

1. **Room-based**: Players join specific game rooms identified by a unique code
2. **Host-controlled**: One player (the host) has special privileges like starting the game
3. **State synchronization**: Game state is synchronized across all connected clients
4. **Turn-based**: Games enforce turn order, allowing only the current player to make moves

## WebSocket Server

The WebSocket server (`server.js`) is responsible for:

- Managing client connections
- Creating and maintaining game rooms
- Routing messages between clients
- Handling disconnections and reconnections

### Key Server Features

- **Room Management**: Create, join, and leave game rooms
- **Player Tracking**: Keep track of connected players and their roles
- **Game State**: Store and synchronize game state across clients
- **Connection Health**: Monitor connection health with ping/pong

## Client Library

The client library (`multiplayer-client.js`) provides a simple interface for games to interact with the WebSocket server:

```javascript
// Create a client instance
const client = new MultiplayerClient({
    serverUrl: 'ws://localhost:8080',
    debug: true
});

// Connect to the server
client.connect()
    .then(() => {
        console.log('Connected to server');
    })
    .catch(error => {
        console.error('Failed to connect:', error);
    });

// Create a room
client.createRoom('game-name', 'Player Name');

// Join a room
client.joinRoom('ABCDEF', 'Player Name');

// Start a game (host only)
client.startGame(initialGameState);

// Send a game action
client.sendGameAction('action_name', gameState);

// Listen for events
client.on('connected', data => {
    console.log('Connected with ID:', data.clientId);
});

client.on('game_action', data => {
    console.log('Received game action:', data);
});
```

### Client Events

The client library emits the following events:

- `connected`: When connected to the server
- `disconnected`: When disconnected from the server
- `reconnected`: When reconnected after a disconnection
- `room_created`: When a room is created
- `player_joined`: When a player joins the room
- `player_left`: When a player leaves the room
- `game_started`: When the game starts
- `game_action`: When a game action is received
- `error`: When an error occurs

## Message Protocol

The WebSocket server and client communicate using JSON messages with the following format:

```json
{
    "type": "message_type",
    "data": { ... }
}
```

### Server to Client Messages

- `connected`: Sent when a client connects to the server
  ```json
  { "type": "connected", "clientId": "unique-id" }
  ```

- `room_created`: Sent when a room is created
  ```json
  { "type": "room_created", "roomId": "ABCDEF", "room": { ... } }
  ```

- `player_joined`: Sent when a player joins a room
  ```json
  { "type": "player_joined", "player": { "id": "id", "name": "name" }, "room": { ... } }
  ```

- `player_left`: Sent when a player leaves a room
  ```json
  { "type": "player_left", "playerId": "id", "room": { ... } }
  ```

- `game_started`: Sent when a game starts
  ```json
  { "type": "game_started", "room": { ... } }
  ```

- `game_action`: Sent when a game action occurs
  ```json
  { "type": "game_action", "playerId": "id", "action": "action_name", "gameState": { ... } }
  ```

- `error`: Sent when an error occurs
  ```json
  { "type": "error", "message": "Error message" }
  ```

### Client to Server Messages

- `create_room`: Create a new game room
  ```json
  { "type": "create_room", "game": "game-name", "playerName": "Player Name" }
  ```

- `join_room`: Join an existing game room
  ```json
  { "type": "join_room", "roomId": "ABCDEF", "playerName": "Player Name" }
  ```

- `leave_room`: Leave the current game room
  ```json
  { "type": "leave_room" }
  ```

- `start_game`: Start the game (host only)
  ```json
  { "type": "start_game", "initialState": { ... } }
  ```

- `game_action`: Send a game action
  ```json
  { "type": "game_action", "action": "action_name", "gameState": { ... } }
  ```

## Implementing Multiplayer in a Game

To implement multiplayer in a game, follow these steps:

### 1. Create the HTML Structure

Create an HTML file with the following sections:

- Lobby section for creating/joining rooms
- Room section for displaying room information and players
- Game section for the actual game

```html
<div id="lobbySection">
    <!-- Player name input and create/join room buttons -->
</div>

<div id="roomSection" class="hidden">
    <!-- Room ID, player list, and start game button -->
</div>

<div id="gameSection" class="hidden">
    <!-- Game board and controls -->
</div>
```

### 2. Initialize the Client

```javascript
const client = new MultiplayerClient({
    debug: true
});

client.connect()
    .then(() => {
        console.log('Connected to server');
    })
    .catch(error => {
        console.error('Failed to connect:', error);
    });
```

### 3. Handle Room Creation and Joining

```javascript
function createRoom() {
    const playerName = document.getElementById('playerName').value;
    client.createRoom('your-game-name', playerName);
}

function joinRoom() {
    const playerName = document.getElementById('playerName').value;
    const roomId = document.getElementById('roomId').value;
    client.joinRoom(roomId, playerName);
}
```

### 4. Handle Game State

Define a game state object that will be synchronized across clients:

```javascript
let gameState = {
    players: [],
    currentPlayerIndex: 0,
    // Game-specific state
};
```

### 5. Implement Turn-Based Logic

```javascript
function isMyTurn() {
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    return currentPlayer === clientState.playerName;
}

function makeMove(move) {
    if (!isMyTurn()) return;
    
    // Update game state
    // ...
    
    // Send the move to other players
    client.sendGameAction('make_move', {
        move: move,
        gameState: gameState
    });
}

// Listen for moves from other players
client.on('game_action', data => {
    if (data.action === 'make_move') {
        // Update local game state
        gameState = data.gameState;
        updateUI();
    }
});
```

### 6. Start the Game (Host Only)

```javascript
function startGame() {
    if (!clientState.isHost) return;
    
    // Initialize game state
    const initialState = {
        players: client.room.players.map(p => p.name),
        currentPlayerIndex: 0,
        // Game-specific initialization
    };
    
    client.startGame(initialState);
}
```

## Deployment

To deploy the multiplayer system:

1. **Server**: Deploy the Node.js WebSocket server to a hosting service that supports WebSockets (e.g., Heroku, DigitalOcean)

2. **Client**: Update the WebSocket server URL in your client code:

```javascript
const client = new MultiplayerClient({
    serverUrl: 'wss://your-server-url.com',
    debug: false
});
```

3. **Security**: For production, consider adding authentication and rate limiting to the WebSocket server

4. **HTTPS**: Ensure your website uses HTTPS, as secure WebSockets (WSS) require HTTPS
