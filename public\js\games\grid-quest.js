/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

let teamMembers = window.parent.names || [];
let positions = {};
let currentPlayer = '';
let remainingPlayers = [];
let guessCount = 0;

function displayTeamMembers() {
    const teamList = document.getElementById('teamList');
    teamList.innerHTML = teamMembers.map(member => 
        `<span class="team-member">${member}</span>`
    ).join('');
}

function startGame() {
    teamMembers = window.parent.names || [];
    if (teamMembers.length < 2) {
        alert('Please add at least two names in the launcher before starting the game.');
        return;
    }

    positions = {};
    remainingPlayers = [...teamMembers];
    guessCount = 0;
    currentPlayer = teamMembers[Math.floor(Math.random() * teamMembers.length)];
    
    const gridSize = 6;
    const cells = gridSize * gridSize;
    const shuffled = [...Array(cells).keys()].sort(() => 0.5 - Math.random());
    teamMembers.forEach((member, index) => {
        positions[member] = shuffled[index];
    });

    createGrid();
    displayTeamMembers();
    updateMessage(`${currentPlayer}, it's your turn to guess!`);
}

function createGrid() {
    const grid = document.getElementById('grid');
    grid.innerHTML = '';
    const letters = ['', 'A', 'B', 'C', 'D', 'E', 'F'];
    
    for (let i = 0; i < 7; i++) {
        for (let j = 0; j < 7; j++) {
            const cell = document.createElement('div');
            cell.className = 'cell';
            if (i === 0 && j === 0) {
                cell.textContent = '';
            } else if (i === 0) {
                cell.textContent = letters[j];
                cell.classList.add('reference');
            } else if (j === 0) {
                cell.textContent = i;
                cell.classList.add('reference');
            } else {
                const index = (i - 1) * 6 + (j - 1);
                cell.onclick = () => makeGuess(index);
                cell.setAttribute('data-reference', `${letters[j]}${i}`);
            }
            grid.appendChild(cell);
        }
    }
}

function makeGuess(index) {
    guessCount++;
    const cells = document.getElementsByClassName('cell');
    const cellIndex = index + 8 + Math.floor(index / 6);
    
    if (positions[currentPlayer] === index) {
        cells[cellIndex].classList.add('hit');
        cells[cellIndex].textContent = currentPlayer.substring(0, 3);
        updateMessage(`${currentPlayer} has found themselves and have been selected!`);
        endGame(currentPlayer);
    } else {
        const hitPlayer = Object.keys(positions).find(member => positions[member] === index);
        if (hitPlayer) {
            cells[cellIndex].classList.add('hit');
            cells[cellIndex].textContent = hitPlayer.substring(0, 3);
            updateMessage(`${currentPlayer} found ${hitPlayer}! ${hitPlayer} is the winner!`);
            endGame(hitPlayer);
        } else {
            cells[cellIndex].classList.add('miss');
            cells[cellIndex].textContent = '×';
            nextTurn();
        }
    }
}

function nextTurn() {
    const currentIndex = teamMembers.indexOf(currentPlayer);
    currentPlayer = teamMembers[(currentIndex + 1) % teamMembers.length];
    updateMessage(`${currentPlayer}, it's your turn to guess!`);
}

function updateMessage(msg) {
    document.getElementById('message').textContent = msg;
}

function endGame(winner) {
    const cells = document.getElementsByClassName('cell');
    for (let i = 0; i < cells.length; i++) {
        if (cells[i].onclick) {
            cells[i].onclick = null;
            const reference = cells[i].getAttribute('data-reference');
            const row = parseInt(reference.substring(1)) - 1;
            const col = reference.charCodeAt(0) - 65;
            const index = row * 6 + col;
            const player = Object.keys(positions).find(member => positions[member] === index);
            if (player && !cells[i].classList.contains('hit')) {
                cells[i].textContent = player.substring(0, 3);
                cells[i].classList.add('hit');
                if (player === winner) {
                    cells[i].classList.add('winner-highlight');
                }
            }
        }
    }

    // Add celebrate class to the message element
    const messageEl = document.getElementById('message');
    messageEl.classList.add('celebrate');

    // Create confetti
    window.parent.confetti?.({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
    });

    // Highlight the winner in the team list
    const teamMembers = document.getElementsByClassName('team-member');
    for (let member of teamMembers) {
        if (member.textContent === winner) {
            member.classList.add('winner-highlight');
        }
    }
}

// Initialize
document.querySelector('button').addEventListener('click', startGame); 