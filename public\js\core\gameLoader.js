/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

class GameLoader {
    constructor(namesManager) {
        this.namesManager = namesManager;
        this.games = [
            { 
                name: 'Grid Quest', 
                icon: '🔍', 
                css: '/css/games/grid-quest.css',
                js: '/js/games/grid-quest.js',
                file: 'grid-quest.html' 
            },
            { 
                name: 'Whirling Warriors', 
                icon: '🌪️', 
                css: '/css/games/whirling-warriors.css',
                js: '/js/games/whirling-warriors.js',
                file: 'whirling-warriors.html' 
            },
            { 
                name: 'Chance Chest Challenge', 
                icon: '🧰', 
                css: '/css/games/chance-chest-challenge.css',
                js: '/js/games/chance-chest-challenge.js',
                file: 'chance-chest-challenge.html' 
            },
            { 
                name: 'Prehistoric Derby', 
                icon: '🦖', 
                css: '/css/games/prehistoric-derby.css',
                js: '/js/games/prehistoric-derby.js',
                file: 'prehistoric-derby.html' 
            },
            { 
                name: 'Mystic Shell Oracle', 
                icon: '🔮', 
                css: '/css/games/shell-oracle.css',
                js: '/js/games/shell-oracle.js',
                file: 'shell-oracle.html' 
            },
            { 
                name: 'Celestial Cascade', 
                icon: '☄️', 
                css: '/css/games/celestial-cascade.css',
                js: '/js/games/celestial-cascade.js',
                file: 'celestial-cascade.html' 
            },
            { 
                name: 'Wheel of Fortune', 
                icon: '🎡', 
                css: '/css/games/wheel-of-fortune.css',
                js: '/js/games/wheel-of-fortune.js',
                file: 'wheel-of-fortune.html' 
            },
            { 
                name: 'Rock Paper Scissors Tournament', 
                icon: '✂️', 
                css: '/css/games/rock-paper-scissors-tournament.css',
                js: '/js/games/rock-paper-scissors-tournament.js',
                file: 'rock-paper-scissors-tournament.html' 
            },
            { 
                name: 'Balloon Pop Parade', 
                icon: '🎈', 
                css: '/css/games/balloon-pop-parade.css',
                js: '/js/games/balloon-pop-parade.js',
                file: 'balloon-pop-parade.html' 
            },
            {
                name: 'Time Bomb Game',
                icon: '💣',
                css: '/css/games/time-bomb.css',
                js: '/js/games/time-bomb.js',
                file: 'time-bomb.html'
            }
        ];

        this.elements = {
            gameFrame: document.getElementById('gameFrame'),
            backButton: document.getElementById('backButton'),
            container: document.querySelector('.container'),
            gameIcons: document.getElementById('gameIcons'),
            launchButton: document.getElementById('launchButton')
        };

        this.initializeEventListeners();
        this.createGameIcons();
    }

    createGameIcons() {
        const fragment = document.createDocumentFragment();
        this.games.forEach(game => {
            const gameIcon = document.createElement('button');
            gameIcon.className = 'gameIcon';
            gameIcon.setAttribute('data-game-name', game.name);
            gameIcon.setAttribute('aria-label', game.name);
            gameIcon.title = game.name;
            gameIcon.innerHTML = game.icon;
            gameIcon.addEventListener('click', () => this.launchGame(game));
            fragment.appendChild(gameIcon);
        });
        this.elements.gameIcons.appendChild(fragment);
    }

    launchGame(game) {
        const names = this.namesManager.getNames();
        if (names.length < 2) {
            alert('Please enter at least two names before launching a game.');
            return;
        }
        
        // Set names in window for iframe access
        window.names = names;
        
        this.elements.gameFrame.src = game.file;
        this.toggleGameVisibility(true);
        this.announceGameLaunch(game.name);
        
        // Add load event listener to initialize game
        this.elements.gameFrame.onload = () => {
            // Pass names to iframe
            this.elements.gameFrame.contentWindow.names = names;
            
            // Initialize game if it has a startGame function
            if (typeof this.elements.gameFrame.contentWindow.startGame === 'function') {
                this.elements.gameFrame.contentWindow.startGame();
            }
        };
    }

    toggleGameVisibility(showGame) {
        this.elements.container.style.display = showGame ? 'none' : 'block';
        this.elements.gameFrame.style.display = showGame ? 'block' : 'none';
        this.elements.backButton.style.display = showGame ? 'block' : 'none';
    }

    announceGameLaunch(gameName) {
        const messageArea = document.createElement('div');
        messageArea.setAttribute('aria-live', 'polite');
        messageArea.className = 'sr-only';
        messageArea.textContent = `Launched ${gameName}`;
        document.body.appendChild(messageArea);
        setTimeout(() => messageArea.remove(), 3000);
    }

    initializeEventListeners() {
        this.elements.backButton.addEventListener('click', () => {
            this.toggleGameVisibility(false);
            this.elements.gameFrame.src = '';
        });

        this.elements.launchButton.addEventListener('click', () => {
            const names = this.namesManager.getNames();
            if (names.length < 2) {
                alert('Please enter at least two names before launching a game.');
                return;
            }
            const randomGame = this.games[Math.floor(Math.random() * this.games.length)];
            this.launchGame(randomGame);
        });

        this.elements.gameFrame.addEventListener('error', () => {
            alert('There was an error loading the game. Please try again.');
            this.toggleGameVisibility(false);
        });
    }
}

export default GameLoader; 