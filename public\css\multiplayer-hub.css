/*!
 * Person Picker Multiplayer Hub (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    --button-hover: #6AADA0;
    --success-color: #4CAF50;
    --error-color: #F44336;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    max-width: 1200px;
    width: 95%;
    margin: 2rem auto;
    background-color: var(--background-color);
    border-radius: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    padding: 2rem;
}

header {
    text-align: center;
    margin-bottom: 2rem;
}

header h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.subheading {
    color: var(--secondary-color);
    font-size: 1.2rem;
    margin-top: 0;
    margin-bottom: 1.5rem;
}

nav {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.nav-button {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.nav-button:hover {
    background-color: var(--primary-color);
}

.connection-status {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.status-indicator {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: bold;
}

.status-indicator.connected {
    background-color: var(--success-color);
    color: white;
}

.status-indicator.disconnected {
    background-color: var(--error-color);
    color: white;
}

.status-indicator.connecting {
    background-color: #FFC107;
    color: var(--text-color);
}

.game-selection {
    margin-bottom: 3rem;
}

.game-selection h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.game-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.game-card {
    background-color: var(--card-background);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.game-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.game-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.game-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.game-card p {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    min-height: 4.5rem;
}

.play-button {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.play-button:hover {
    background-color: var(--button-hover);
}

.coming-soon {
    opacity: 0.7;
    pointer-events: none;
}

.coming-soon-label {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: var(--secondary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: bold;
}

.how-to-play {
    margin-bottom: 3rem;
}

.how-to-play h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.step {
    background-color: var(--card-background);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.3s ease;
    position: relative;
}

.step:hover {
    transform: translateY(-5px);
}

.step-number {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 50%;
    font-weight: bold;
    margin: 0 auto 1rem;
}

.step h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.step p {
    color: var(--text-color);
}

footer {
    text-align: center;
    color: var(--text-color);
    font-size: 0.9rem;
    margin-top: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 1.5rem;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .game-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.8rem;
    }
    
    .subheading {
        font-size: 1rem;
    }
    
    nav {
        flex-direction: column;
        gap: 0.5rem;
    }
}
