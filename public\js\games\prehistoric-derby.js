/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

const dinoTypes = ['🦖', '🦕', '🦖', '🦕', '🦖', '🦕', '🦖', '🦕', '🦖', '🦕', '🦖', '🦕'];
let dinosaurs = [];
let raceInterval;
let finishedCount = 0;
let trackWidth;
let teamMembers = window.parent.names || [];

function createDinosaurs() {
    if (teamMembers.length < 2) {
        alert('Please add at least two names in the launcher before starting the game.');
        return false;
    }

    const raceTrack = document.getElementById('raceTrack');
    raceTrack.innerHTML = '<div id="finishLine"></div>';
    
    // Adjust track height based on number of participants
    const trackHeight = Math.max(200, Math.min(teamMembers.length * 55, 600));
    raceTrack.style.height = `${trackHeight}px`;
    
    // Set track width
    trackWidth = raceTrack.clientWidth - 80; // 60px for dinosaur width + 20px for finish line

    const dinoHeight = Math.min(60, (trackHeight / teamMembers.length) - 5);

    dinosaurs = teamMembers.map((member, index) => {
        const dinoContainer = document.createElement('div');
        dinoContainer.style.position = 'absolute';
        dinoContainer.style.top = `${(index * (trackHeight / teamMembers.length)) + ((trackHeight / teamMembers.length - dinoHeight) / 2)}px`;

        const dino = document.createElement('div');
        dino.className = 'dinosaur';
        dino.style.backgroundImage = `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">${dinoTypes[index % dinoTypes.length]}</text></svg>')`;
        dino.style.width = `${dinoHeight}px`;
        dino.style.height = `${dinoHeight}px`;
        dinoContainer.appendChild(dino);

        const nameTag = document.createElement('div');
        nameTag.className = 'dinoName';
        nameTag.textContent = member;
        dinoContainer.appendChild(nameTag);

        raceTrack.appendChild(dinoContainer);
        return { element: dinoContainer, member, position: 0, finished: false };
    });
    return true;
}

function startRace() {
    if (!createDinosaurs()) return;
    finishedCount = 0;
    document.getElementById('startButton').disabled = true;
    document.getElementById('winnerMessage').textContent = '';
    document.getElementById('winnerMessage').classList.remove('show');
    updateMessage("The race is on!");
    raceInterval = setInterval(updateRace, 50);
}

function updateMessage(message) {
    document.getElementById('message').textContent = message;
}

function updateRace() {
    dinosaurs.forEach(dino => {
        if (!dino.finished) {
            dino.position += Math.random() * 5;
            if (dino.position >= trackWidth) {
                dino.position = trackWidth;
                dino.finished = true;
                finishedCount++;
                if (finishedCount === 1) {
                    clearInterval(raceInterval);
                    announceWinner(dino);
                }
            }
            dino.element.style.left = `${dino.position}px`;
        }
    });
}

function announceWinner(winner) {
    updateMessage(`${winner.member} crosses the finish line first!`);
    document.getElementById('startButton').disabled = false;
    
    window.parent.confetti?.({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
    });
    
    const winnerMessage = document.getElementById('winnerMessage');
    winnerMessage.textContent = `🎉 ${winner.member} is the prehistoric champion! 🎉`;
    winnerMessage.classList.add('show');
}

// Initialize
document.getElementById('startButton').addEventListener('click', startRace); 