<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Person Picker - Random Name Selector</title>
    
    <!-- Cache Control -->
    <meta http-equiv="Cache-Control" content="public, max-age=31536000">
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Person Picker is the ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more. Try our fun selection games now!">
    <meta name="keywords" content="random name picker, name selector, random picker, team selection, classroom tool">
    <meta name="author" content="Person Picker">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://personpicker.com/">
    <meta property="og:title" content="Random Person Picker - Fair Selection Made Fun">
    <meta property="og:description" content="The ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more.">
    <meta property="og:image" content="https://personpicker.com/android-chrome-512x512.png">
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://personpicker.com/">
    <meta name="twitter:title" content="Random Person Picker - Fair Selection Made Fun">
    <meta name="twitter:description" content="The ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more.">
    <meta name="twitter:image" content="https://personpicker.com/android-chrome-512x512.png">
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/public/css/styles.css">
    <link rel="stylesheet" href="/public/css/welcome.css">
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-CXC0589Q5T"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-CXC0589Q5T');
    </script>
</head>
<body>
    <div class="welcome-container">
        <div class="welcome-header">
            <h1>Person Picker</h1>
            <p class="subheading">The ultimate random name selector for fair decisions</p>
        </div>
        
        <div class="welcome-intro">
            <p>Welcome to Person Picker! Choose how you want to play:</p>
        </div>
        
        <div class="mode-selection">
            <div class="mode-card">
                <div class="mode-icon">👤</div>
                <h2>Single Player</h2>
                <p>One person enters all names and controls the game. Perfect for in-person groups.</p>
                <ul>
                    <li>Enter all participant names</li>
                    <li>Choose from multiple fun games</li>
                    <li>Great for classrooms and meetings</li>
                </ul>
                <a href="/single-player.html" class="mode-button">Start Single Player</a>
            </div>
            
            <div class="mode-card">
                <div class="mode-icon">👥</div>
                <h2>Multiplayer</h2>
                <p>Multiple people join remotely and play together. Perfect for remote teams.</p>
                <ul>
                    <li>Create or join a game room</li>
                    <li>Each player joins from their device</li>
                    <li>Play together in real-time</li>
                </ul>
                <a href="/multiplayer.html" class="mode-button">Start Multiplayer</a>
            </div>
        </div>
        
        <div class="welcome-features">
            <h2>Perfect For...</h2>
            <div class="features-grid">
                <div class="feature">
                    <h3>Team Collaboration</h3>
                    <p>Assign roles or tasks fairly to team members, enhancing engagement.</p>
                </div>
                <div class="feature">
                    <h3>Classroom Activities</h3>
                    <p>Select students for presentations or group assignments fairly.</p>
                </div>
                <div class="feature">
                    <h3>Decision Making</h3>
                    <p>Resolve conflicts or make unbiased selections effortlessly.</p>
                </div>
                <div class="feature">
                    <h3>Remote Teams</h3>
                    <p>Connect and play together no matter where team members are located.</p>
                </div>
            </div>
        </div>
        
        <footer>
            <p>© 2025 Person Picker - Your Trusted Random Name Selector. All rights reserved.</p>
        </footer>
    </div>
</body>
</html>
